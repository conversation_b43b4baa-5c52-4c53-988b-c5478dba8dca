# Settlement 扫雷游戏结算功能实现说明

## 概述

根据API.md文档中的Settlement消息定义，实现了扫雷游戏的结算页面功能。当收到WebSocket的"Settlement"消息时，会弹出专门的结算页面dialog并更新游戏结算页面的排行榜。

## 实现内容

### 1. 数据结构定义 (GameBean.ts)

在`assets/scripts/bean/GameBean.ts`中添加了扫雷游戏结算相关的接口定义：

```typescript
// 扫雷游戏结算通知
export interface MinesweeperSettlement {
    gameType: string; // 游戏类型标识 "minesweeper"
    totalRounds: number; // 总回合数
    playerCount: number; // 玩家数量
    finalRanking: PlayerFinalResult[]; // 最终排名列表
    fee: number; // 房间费用
    gameStats: GameStats; // 游戏统计信息
}

// 游戏统计信息
export interface GameStats {
    mapSize: string; // 地图大小 "8x8"
    mineCount: number; // 地雷总数
    revealedCount: number; // 已揭示的方块数
}

// 玩家最终结果结构（扫雷游戏）
export interface PlayerFinalResult {
    userId: string; // 玩家ID
    totalScore: number; // 总得分
    mineHits: number; // 踩雷数（用于排名，标记后踩雷不计入）
    rank: number; // 最终排名
    coinChg: number; // 金币变化（正数为获得，0为无变化）
}
```

### 2. 结算页面控制器 (SettlementDialogController.ts)

创建了专门用于扫雷游戏结算的dialog控制器：

#### 主要功能
- 显示游戏统计信息（总回合数、地图大小、地雷数量、已揭示方块数）
- 显示玩家排行榜（排名、昵称、得分、踩雷次数、金币变化）
- 支持倒计时自动关闭（10秒）
- 更新玩家金币
- 播放结算音效

#### 主要属性
```typescript
@property(cc.Node) boardBg: cc.Node = null // 背景
@property(cc.Node) layoutNode: cc.Node = null // 排行榜布局
@property(cc.Prefab) rankingItem: cc.Prefab = null // 排行榜项目预制体
@property(cc.Label) totalRoundsLabel: cc.Label = null // 总回合数标签
@property(cc.Label) mapSizeLabel: cc.Label = null // 地图大小标签
@property(cc.Label) mineCountLabel: cc.Label = null // 地雷数量标签
@property(cc.Label) revealedCountLabel: cc.Label = null // 已揭示方块标签
```

#### 主要方法
- `show(settlementData: MinesweeperSettlement, backCallback: Function)`: 显示结算页面
- `updateRankingList(finalRanking: PlayerFinalResult[])`: 更新排行榜
- `hide(bool: boolean = false)`: 隐藏结算页面

### 3. 排行榜项目控制器 (SettlementRankingItemController.ts)

创建了专门用于显示排行榜项目的控制器：

#### 主要功能
- 显示玩家排名（前三名有特殊颜色）
- 显示玩家昵称
- 显示玩家得分
- 显示踩雷次数
- 显示金币变化（正数绿色，负数红色）
- 支持头像显示
- 支持高亮当前玩家

#### 主要属性
```typescript
@property(cc.Label) rankLabel: cc.Label = null // 排名标签
@property(cc.Label) nameLabel: cc.Label = null // 玩家昵称标签
@property(cc.Label) scoreLabel: cc.Label = null // 得分标签
@property(cc.Label) mineHitsLabel: cc.Label = null // 踩雷次数标签
@property(cc.Label) coinChangeLabel: cc.Label = null // 金币变化标签
@property(cc.Sprite) avatarSprite: cc.Sprite = null // 头像精灵
```

### 4. 游戏页面控制器更新 (GamePageController.ts)

在`assets/scripts/game/GamePageController.ts`中添加了：

#### 新增属性
```typescript
@property(SettlementDialogController)
settlementDialogController: SettlementDialogController = null //扫雷游戏结算弹窗
```

#### 新增方法
- `setSettlementDialog(settlementData: MinesweeperSettlement)`: 处理扫雷游戏结算
- `playSettlementAudio(settlementData: MinesweeperSettlement)`: 播放结算音效

### 5. 全局消息处理更新 (GlobalManagerController.ts)

在`assets/scripts/GlobalManagerController.ts`中更新了Settlement消息的处理逻辑：

```typescript
case MessageId.MsgTypeSettlement: //大结算
    // 根据数据结构判断是普通结算还是扫雷游戏结算
    if (messageBean.data.gameType === 'minesweeper') {
        // 扫雷游戏结算
        let minesweeperSettlement: MinesweeperSettlement = messageBean.data
        this.gamePageController.setSettlementDialog(minesweeperSettlement)
    } else {
        // 普通游戏结算
        let noticeSettlement: NoticeSettlement = messageBean.data
        this.gamePageController.setCongratsDialog(noticeSettlement)
    }
    break
```

### 6. 测试脚本 (SettlementTest.ts)

创建了测试脚本用于验证Settlement消息的处理：

#### 功能
- 发送测试的扫雷游戏结算消息
- 发送测试的普通游戏结算消息
- 提供控制台调用方法进行测试

#### 使用方法
```javascript
// 在浏览器控制台中调用
SettlementTest.testMinesweeperSettlement(); // 测试扫雷结算
SettlementTest.testNormalSettlement(); // 测试普通结算
```

## 消息流程

1. **接收消息**: WebSocketManager接收到Settlement消息
2. **消息分发**: 通过EventCenter发送ReceiveMessage事件
3. **消息处理**: GlobalManagerController接收事件并处理
4. **类型判断**: 根据`data.gameType`判断是否为扫雷游戏结算
5. **显示结算**: 调用相应的结算dialog显示结算信息
6. **更新数据**: 更新玩家金币和游戏状态

## API消息格式

根据API.md文档，Settlement消息的格式为：

```json
{
  "msgId": "Settlement",
  "code": 0,
  "msg": "success",
  "data": {
    "gameType": "minesweeper",
    "totalRounds": 5,
    "playerCount": 2,
    "finalRanking": [
      {
        "userId": "player_001",
        "totalScore": 25,
        "mineHits": 1,
        "rank": 1,
        "coinChg": 180
      }
    ],
    "fee": 100,
    "gameStats": {
      "mapSize": "8x8",
      "mineCount": 13,
      "revealedCount": 45
    }
  }
}
```

## 注意事项

1. **兼容性**: 保持了对原有普通游戏结算的兼容性
2. **UI组件**: 需要在编辑器中设置相应的UI组件引用
3. **预制体**: 需要创建排行榜项目的预制体并设置相应的子节点
4. **头像加载**: 头像加载功能需要根据项目的具体实现方式进行调整
5. **音效**: 使用了现有的AudioManager播放胜利/失败音效

## 文件清单

- `assets/scripts/bean/GameBean.ts` - 数据结构定义
- `assets/scripts/game/SettlementDialogController.ts` - 结算页面控制器
- `assets/scripts/pfb/SettlementRankingItemController.ts` - 排行榜项目控制器
- `assets/scripts/game/GamePageController.ts` - 游戏页面控制器（更新）
- `assets/scripts/GlobalManagerController.ts` - 全局消息处理（更新）
- `assets/scripts/test/SettlementTest.ts` - 测试脚本
- `Settlement实现说明.md` - 本说明文档
