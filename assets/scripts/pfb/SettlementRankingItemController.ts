// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { PlayerFinalResult, RoomUser } from "../bean/GameBean";
import { GameMgr } from "../common/GameMgr";

const { ccclass, property } = cc._decorator;

//扫雷游戏结算排行榜项目控制器
@ccclass
export default class SettlementRankingItemController extends cc.Component {

    @property(cc.Label)
    rankLabel: cc.Label = null // 排名标签

    @property(cc.Label)
    nameLabel: cc.Label = null // 玩家昵称标签

    @property(cc.Label)
    scoreLabel: cc.Label = null // 得分标签

    @property(cc.Label)
    mineHitsLabel: cc.Label = null // 踩雷次数标签

    @property(cc.Label)
    coinChangeLabel: cc.Label = null // 金币变化标签

    @property(cc.Sprite)
    avatarSprite: cc.Sprite = null // 头像精灵

    @property(cc.Node)
    rankIcon: cc.Node = null // 排名图标（前三名可能有特殊图标）

    onLoad() {
        // 如果没有在编辑器中设置，尝试自动查找子节点
        if (!this.rankLabel) {
            const rankNode = this.node.getChildByName('rank_label');
            if (rankNode) {
                this.rankLabel = rankNode.getComponent(cc.Label);
            }
        }

        if (!this.nameLabel) {
            const nameNode = this.node.getChildByName('name_label');
            if (nameNode) {
                this.nameLabel = nameNode.getComponent(cc.Label);
            }
        }

        if (!this.scoreLabel) {
            const scoreNode = this.node.getChildByName('score_label');
            if (scoreNode) {
                this.scoreLabel = scoreNode.getComponent(cc.Label);
            }
        }

        if (!this.mineHitsLabel) {
            const mineHitsNode = this.node.getChildByName('mine_hits_label');
            if (mineHitsNode) {
                this.mineHitsLabel = mineHitsNode.getComponent(cc.Label);
            }
        }

        if (!this.coinChangeLabel) {
            const coinChangeNode = this.node.getChildByName('coin_change_label');
            if (coinChangeNode) {
                this.coinChangeLabel = coinChangeNode.getComponent(cc.Label);
            }
        }

        if (!this.avatarSprite) {
            const avatarNode = this.node.getChildByName('avatar');
            if (avatarNode) {
                this.avatarSprite = avatarNode.getComponent(cc.Sprite);
            }
        }

        if (!this.rankIcon) {
            this.rankIcon = this.node.getChildByName('rank_icon');
        }
    }

    /**
     * 设置排行榜项目数据
     * @param playerData 玩家结算数据
     * @param gameUsers 游戏中的用户信息列表
     * @param displayRank 显示排名（用于显示，可能与实际rank不同）
     */
    createData(playerData: PlayerFinalResult, gameUsers: RoomUser[], displayRank: number = 0) {
        // 查找对应的用户信息
        const userInfo = gameUsers.find(user => user.userId === playerData.userId);
        
        // 设置排名
        if (this.rankLabel) {
            const rank = displayRank > 0 ? displayRank : playerData.rank;
            this.rankLabel.string = `${rank}`;
            
            // 根据排名设置颜色
            if (rank === 1) {
                this.rankLabel.node.color = cc.Color.YELLOW; // 金色
            } else if (rank === 2) {
                this.rankLabel.node.color = cc.Color.WHITE; // 银色
            } else if (rank === 3) {
                this.rankLabel.node.color = new cc.Color(205, 127, 50); // 铜色
            } else {
                this.rankLabel.node.color = cc.Color.WHITE; // 默认白色
            }
        }

        // 设置昵称
        if (this.nameLabel) {
            this.nameLabel.string = userInfo?.nickName || `玩家${playerData.userId}`;
        }

        // 设置得分
        if (this.scoreLabel) {
            this.scoreLabel.string = `${playerData.totalScore}`;
        }

        // 设置踩雷次数
        if (this.mineHitsLabel) {
            this.mineHitsLabel.string = `${playerData.mineHits}`;
        }

        // 设置金币变化
        if (this.coinChangeLabel) {
            const coinChange = playerData.coinChg;
            this.coinChangeLabel.string = coinChange > 0 ? `+${coinChange}` : `${coinChange}`;
            
            // 根据金币变化设置颜色
            if (coinChange > 0) {
                this.coinChangeLabel.node.color = cc.Color.GREEN;
            } else if (coinChange < 0) {
                this.coinChangeLabel.node.color = cc.Color.RED;
            } else {
                this.coinChangeLabel.node.color = cc.Color.WHITE;
            }
        }

        // 设置排名图标（如果有的话）
        if (this.rankIcon) {
            const rank = displayRank > 0 ? displayRank : playerData.rank;
            if (rank <= 3) {
                this.rankIcon.active = true;
                // 这里可以根据排名设置不同的图标
                // 具体实现根据项目的图标资源
            } else {
                this.rankIcon.active = false;
            }
        }

        // 设置头像
        this.loadAvatar(userInfo?.avatar);

        GameMgr.Console.Log(`设置排行榜项目数据: 排名${playerData.rank}, 玩家${userInfo?.nickName}, 得分${playerData.totalScore}, 踩雷${playerData.mineHits}次, 金币变化${playerData.coinChg}`);
    }

    /**
     * 加载玩家头像
     * @param avatarUrl 头像URL
     */
    private loadAvatar(avatarUrl?: string) {
        if (!this.avatarSprite || !avatarUrl) {
            return;
        }

        // 这里需要根据项目的头像加载方式来实现
        // 可能是从网络加载，也可能是从本地资源加载
        GameMgr.Console.Log(`加载头像: ${avatarUrl}`);
        
        // 示例：如果是本地资源
        // cc.resources.load(avatarUrl, cc.SpriteFrame, (err, spriteFrame) => {
        //     if (!err && spriteFrame && this.avatarSprite) {
        //         this.avatarSprite.spriteFrame = spriteFrame;
        //     }
        // });

        // 示例：如果是网络图片
        // cc.assetManager.loadRemote(avatarUrl, (err, texture) => {
        //     if (!err && texture && this.avatarSprite) {
        //         this.avatarSprite.spriteFrame = new cc.SpriteFrame(texture);
        //     }
        // });
    }

    /**
     * 设置项目是否高亮（比如当前玩家）
     * @param highlight 是否高亮
     */
    setHighlight(highlight: boolean) {
        if (highlight) {
            // 设置高亮效果，比如改变背景色或添加边框
            this.node.color = new cc.Color(255, 255, 0, 100); // 淡黄色背景
        } else {
            this.node.color = cc.Color.WHITE;
        }
    }
}
