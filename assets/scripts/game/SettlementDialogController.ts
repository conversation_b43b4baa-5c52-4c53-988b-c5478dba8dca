// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { MinesweeperSettlement, PlayerFinalResult } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import { EventType } from "../common/EventCenter";
import { GameMgr } from "../common/GameMgr";
import { AutoMessageBean, AutoMessageId } from "../net/MessageBaseBean";
import SettlementRankingItemController from "../pfb/SettlementRankingItemController";
import { Config } from "../util/Config";
import { Tools } from "../util/Tools";

const { ccclass, property } = cc._decorator;

//扫雷游戏结算页面
@ccclass
export default class SettlementDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null
    @property(cc.Node)
    contentLay: cc.Node = null
    @property(cc.Node)
    backBtn: cc.Node = null
    @property(cc.Node)
    public layoutNode: cc.Node = null;//存放排行榜列表的布局
    @property(cc.Prefab)
    rankingItem: cc.Prefab = null;//排行榜列表的 item

    // 游戏统计信息显示
    @property(cc.Label)
    totalRoundsLabel: cc.Label = null
    @property(cc.Label)
    mapSizeLabel: cc.Label = null
    @property(cc.Label)
    mineCountLabel: cc.Label = null
    @property(cc.Label)
    revealedCountLabel: cc.Label = null

    countdownTimeLabel: cc.Label = null
    countdownInterval: number = null;//倒计时的 id

    backCallback: Function = null //隐藏弹窗的回调
    seconds: number = 10;//倒计时 10 秒

    onLoad() {
        this.countdownTimeLabel = this.backBtn.getChildByName('buttonLabel_time').getComponent(cc.Label);
    }

    protected onEnable(): void {
        this.updateCountdownLabel(this.seconds);
        Tools.setCountDownTimeLabel(this.backBtn)
    }

    start() {
        //backBtn 按钮点击事件
        Tools.greenButton(this.backBtn, () => {
            this.hide(true)
        })
    }

    show(settlementData: MinesweeperSettlement, backCallback: Function) {
        this.backCallback = backCallback
        this.node.active = true
        this.boardBg.scale = 0
        this._setData(settlementData)
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 1 })
            .start();
    }

    _setData(settlementData: MinesweeperSettlement) {
        // 更新玩家金币（找到当前玩家的结算数据）
        const currentUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
        const myResult = settlementData.finalRanking.find(player => player.userId === currentUserId);
        if (myResult) {
            // 更新玩家金币
            GlobalBean.GetInstance().loginData.userInfo.coin += myResult.coinChg;
        }

        // 显示游戏统计信息
        if (this.totalRoundsLabel) {
            this.totalRoundsLabel.string = `总回合数: ${settlementData.totalRounds}`;
        }
        if (this.mapSizeLabel) {
            this.mapSizeLabel.string = `地图大小: ${settlementData.gameStats.mapSize}`;
        }
        if (this.mineCountLabel) {
            this.mineCountLabel.string = `地雷数量: ${settlementData.gameStats.mineCount}`;
        }
        if (this.revealedCountLabel) {
            this.revealedCountLabel.string = `已揭示方块: ${settlementData.gameStats.revealedCount}`;
        }

        // 更新排行榜
        this.updateRankingList(settlementData.finalRanking);
        
        // 开始倒计时
        this.startCountdown(10);
    }

    updateRankingList(finalRanking: PlayerFinalResult[]) {
        if (!this.layoutNode) {
            GameMgr.Console.Log('layoutNode 未设置，无法显示排行榜');
            return;
        }

        // 清空现有列表
        this.layoutNode.removeAllChildren();

        // 按排名排序
        const sortedRanking = finalRanking.sort((a, b) => a.rank - b.rank);

        // 创建排行榜项目
        for (let i = 0; i < sortedRanking.length; i++) {
            const playerData = sortedRanking[i];
            
            if (this.rankingItem) {
                const item = cc.instantiate(this.rankingItem);
                this.layoutNode.addChild(item);
                
                // 设置排行榜项目数据
                setTimeout(() => {
                    const controller = item.getComponent(SettlementRankingItemController);
                    if (controller) {
                        const gameUsers = GlobalBean.GetInstance().noticeStartGame?.users || [];
                        controller.createData(playerData, gameUsers, i + 1);

                        // 如果是当前玩家，设置高亮
                        const currentUserId = GlobalBean.GetInstance().loginData.userInfo.userId;
                        if (playerData.userId === currentUserId) {
                            controller.setHighlight(true);
                        }
                    } else {
                        // 如果没有控制器组件，使用原来的方法
                        this.setRankingItemData(item, playerData, i + 1);
                    }
                }, 100);
            } else {
                // 如果没有预制体，创建简单的文本显示
                const item = new cc.Node();
                const label = item.addComponent(cc.Label);
                label.string = `第${playerData.rank}名: 得分${playerData.totalScore} 踩雷${playerData.mineHits}次 金币变化${playerData.coinChg > 0 ? '+' : ''}${playerData.coinChg}`;
                label.fontSize = 24;
                this.layoutNode.addChild(item);
            }
        }
    }

    setRankingItemData(item: cc.Node, playerData: PlayerFinalResult, displayRank: number) {
        // 尝试找到对应的用户信息
        const gameUsers = GlobalBean.GetInstance().noticeStartGame?.users || [];
        const userInfo = gameUsers.find(user => user.userId === playerData.userId);
        
        // 设置排名
        const rankLabel = item.getChildByName('rank_label')?.getComponent(cc.Label);
        if (rankLabel) {
            rankLabel.string = `${displayRank}`;
        }

        // 设置昵称
        const nameLabel = item.getChildByName('name_label')?.getComponent(cc.Label);
        if (nameLabel) {
            nameLabel.string = userInfo?.nickName || `玩家${playerData.userId}`;
        }

        // 设置得分
        const scoreLabel = item.getChildByName('score_label')?.getComponent(cc.Label);
        if (scoreLabel) {
            scoreLabel.string = `${playerData.totalScore}`;
        }

        // 设置踩雷次数
        const mineHitsLabel = item.getChildByName('mine_hits_label')?.getComponent(cc.Label);
        if (mineHitsLabel) {
            mineHitsLabel.string = `${playerData.mineHits}`;
        }

        // 设置金币变化
        const coinChangeLabel = item.getChildByName('coin_change_label')?.getComponent(cc.Label);
        if (coinChangeLabel) {
            const coinChange = playerData.coinChg;
            coinChangeLabel.string = coinChange > 0 ? `+${coinChange}` : `${coinChange}`;
            coinChangeLabel.node.color = coinChange > 0 ? cc.Color.GREEN : cc.Color.RED;
        }

        // 设置头像（如果有的话）
        const avatarSprite = item.getChildByName('avatar')?.getComponent(cc.Sprite);
        if (avatarSprite && userInfo?.avatar) {
            // 这里可以加载头像，具体实现根据项目的头像加载方式
            GameMgr.Console.Log(`加载头像: ${userInfo.avatar}`);
        }
    }

    // bool 在隐藏的时候是否返回大厅
    hide(bool: boolean = false) {
        if (this.backCallback) {
            this.backCallback()
        }
        GameMgr.Console.Log('隐藏扫雷结算页面')
        // 执行缩放动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { scale: 0 })
            .call(() => {
                this.node.active = false
                if (bool) {
                    GlobalBean.GetInstance().cleanData()
                    let autoMessageBean: AutoMessageBean = {
                        'msgId': AutoMessageId.JumpHallPage,//跳转进大厅页面
                        'data': { 'type': 2 }//2是结算弹窗跳转的
                    }
                    GameMgr.Event.Send(EventType.AutoMessage, autoMessageBean);
                }
            })
            .start();
    }

    protected onDisable(): void {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
    }

    startCountdown(seconds: number) {
        let remainingSeconds = seconds;
        this.updateCountdownLabel(remainingSeconds);

        this.countdownInterval = setInterval(() => {
            remainingSeconds--;

            if (remainingSeconds <= 0) {
                clearInterval(this.countdownInterval);
                this.countdownInterval = null
                // 倒计时结束时的处理逻辑
                this.hide(true)
                return
            }
            this.updateCountdownLabel(remainingSeconds);
        }, 1000);
    }

    updateCountdownLabel(seconds: number) {
        if (this.countdownTimeLabel) {
            this.countdownTimeLabel.string = `（${seconds}s）`;
        }
    }
}
