// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { MinesweeperSettlement } from "../bean/GameBean";
import { EventType } from "../common/EventCenter";
import { GameMgr } from "../common/GameMgr";
import { ReceivedMessageBean } from "../net/MessageBaseBean";
import { MessageId } from "../net/MessageId";

const { ccclass, property } = cc._decorator;

//扫雷游戏Settlement消息测试
@ccclass
export default class SettlementTest extends cc.Component {

    @property(cc.Button)
    testButton: cc.Button = null

    onLoad() {
        if (!this.testButton) {
            // 尝试查找测试按钮
            const buttonNode = this.node.getChildByName('test_button');
            if (buttonNode) {
                this.testButton = buttonNode.getComponent(cc.Button);
            }
        }
    }

    start() {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendTestSettlementMessage, this);
        }
    }

    /**
     * 发送测试的Settlement消息
     */
    sendTestSettlementMessage() {
        GameMgr.Console.Log('发送测试Settlement消息');

        // 创建测试的扫雷游戏结算数据
        const testSettlementData: MinesweeperSettlement = {
            gameType: "minesweeper",
            totalRounds: 5,
            playerCount: 2,
            finalRanking: [
                {
                    userId: "player_001",
                    totalScore: 25,
                    mineHits: 1,
                    rank: 1,
                    coinChg: 180
                },
                {
                    userId: "player_002", 
                    totalScore: 15,
                    mineHits: 2,
                    rank: 2,
                    coinChg: 0
                }
            ],
            fee: 100,
            gameStats: {
                mapSize: "8x8",
                mineCount: 13,
                revealedCount: 45
            }
        };

        // 构造消息
        const messageBean: ReceivedMessageBean = {
            msgId: MessageId.MsgTypeSettlement,
            code: 0,
            msg: "success",
            data: testSettlementData
        };

        // 发送消息
        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);
    }

    /**
     * 发送测试的普通Settlement消息（用于对比）
     */
    sendTestNormalSettlementMessage() {
        GameMgr.Console.Log('发送测试普通Settlement消息');

        // 创建测试的普通结算数据（确保有users数组）
        const testNormalSettlementData = {
            users: [
                {
                    userId: "player_001",
                    coinChg: 180,
                    coin: 1180,
                    rank: 1,
                    score: 25
                },
                {
                    userId: "player_002",
                    coinChg: 0,
                    coin: 1000,
                    rank: 2,
                    score: 15
                }
            ]
        };

        // 构造消息
        const messageBean: ReceivedMessageBean = {
            msgId: MessageId.MsgTypeSettlement,
            code: 0,
            msg: "success",
            data: testNormalSettlementData
        };

        // 发送消息
        GameMgr.Event.Send(EventType.ReceiveMessage, messageBean);
    }

    /**
     * 创建测试按钮（如果场景中没有的话）
     */
    createTestButtons() {
        // 创建扫雷结算测试按钮
        const minesweeperTestButton = new cc.Node('MinesweeperSettlementTest');
        const minesweeperButton = minesweeperTestButton.addComponent(cc.Button);
        const minesweeperLabel = minesweeperTestButton.addComponent(cc.Label);
        minesweeperLabel.string = '测试扫雷结算';
        minesweeperLabel.fontSize = 24;
        minesweeperTestButton.setPosition(0, 100);
        minesweeperTestButton.on('click', this.sendTestSettlementMessage, this);
        this.node.addChild(minesweeperTestButton);

        // 创建普通结算测试按钮
        const normalTestButton = new cc.Node('NormalSettlementTest');
        const normalButton = normalTestButton.addComponent(cc.Button);
        const normalLabel = normalTestButton.addComponent(cc.Label);
        normalLabel.string = '测试普通结算';
        normalLabel.fontSize = 24;
        normalTestButton.setPosition(0, 50);
        normalTestButton.on('click', this.sendTestNormalSettlementMessage, this);
        this.node.addChild(normalTestButton);

        GameMgr.Console.Log('创建了测试按钮');
    }

    /**
     * 在游戏运行时可以通过控制台调用此方法进行测试
     */
    static testMinesweeperSettlement() {
        const testComponent = cc.find('Canvas')?.getComponent(SettlementTest);
        if (testComponent) {
            testComponent.sendTestSettlementMessage();
        } else {
            console.log('未找到SettlementTest组件');
        }
    }

    /**
     * 在游戏运行时可以通过控制台调用此方法进行测试
     */
    static testNormalSettlement() {
        const testComponent = cc.find('Canvas')?.getComponent(SettlementTest);
        if (testComponent) {
            testComponent.sendTestNormalSettlementMessage();
        } else {
            console.log('未找到SettlementTest组件');
        }
    }
}
